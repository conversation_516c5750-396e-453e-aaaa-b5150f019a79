@echo off
title iDRAC KVM Auto-Installer Example

echo ========================================
echo   iDRAC KVM Auto-Installer Example
echo ========================================
echo.

echo This script demonstrates how to automatically install
echo KVM virtualization system via iDRAC
echo.

REM Example configuration
set EXAMPLE_IDRAC_IP=*************
set EXAMPLE_USERNAME=root
set EXAMPLE_PASSWORD=calvin
set EXAMPLE_ISO_PATH=C:\ISOs\ubuntu-22.04-server-amd64.iso

echo Example Configuration:
echo iDRAC IP: %EXAMPLE_IDRAC_IP%
echo Username: %EXAMPLE_USERNAME%
echo Password: %EXAMPLE_PASSWORD%
echo ISO Path: %EXAMPLE_ISO_PATH%
echo.

echo Available Commands:
echo.

echo 1. Scan network for iDRAC devices:
echo    python test\idrac_web_manager.py --scan 192.168.1
echo.

echo 2. Test iDRAC connection:
echo    python test\idrac_web_manager.py --host %EXAMPLE_IDRAC_IP% --info
echo.

echo 3. Open iDRAC web interface:
echo    python test\idrac_web_manager.py --host %EXAMPLE_IDRAC_IP% --open
echo.

echo 4. Mount ISO file only:
echo    python test\idrac_web_manager.py --host %EXAMPLE_IDRAC_IP% --mount-iso "%EXAMPLE_ISO_PATH%"
echo.

echo 5. Power control:
echo    python test\idrac_web_manager.py --host %EXAMPLE_IDRAC_IP% --power reset
echo.

echo 6. Full automatic KVM installation:
echo    python test\idrac_web_manager.py --host %EXAMPLE_IDRAC_IP% --install-kvm "%EXAMPLE_ISO_PATH%"
echo.

echo ========================================
echo.

set /p CHOICE="Do you want to run a test command? (y/N): "
if /i "%CHOICE%"=="y" (
    echo.
    echo Select test command:
    echo 1. Scan network
    echo 2. Test connection
    echo 3. Open web interface
    echo.
    
    set /p TEST_CHOICE="Enter choice (1-3): "
    
    if "%TEST_CHOICE%"=="1" (
        set /p NETWORK="Enter network (e.g., 192.168.1): "
        echo Running: python test\idrac_web_manager.py --scan !NETWORK!
        python test\idrac_web_manager.py --scan !NETWORK!
    ) else if "%TEST_CHOICE%"=="2" (
        set /p IDRAC_IP="Enter iDRAC IP: "
        echo Running: python test\idrac_web_manager.py --host !IDRAC_IP! --info
        python test\idrac_web_manager.py --host !IDRAC_IP! --info
    ) else if "%TEST_CHOICE%"=="3" (
        set /p IDRAC_IP="Enter iDRAC IP: "
        echo Running: python test\idrac_web_manager.py --host !IDRAC_IP! --open
        python test\idrac_web_manager.py --host !IDRAC_IP! --open
    ) else (
        echo Invalid choice
    )
)

echo.
echo For full automatic installation, prepare:
echo 1. Download Ubuntu Server ISO from https://ubuntu.com/download/server
echo 2. Note your iDRAC IP address and credentials
echo 3. Run: python test\idrac_web_manager.py --host [IP] --install-kvm "[ISO_PATH]"
echo.

pause
