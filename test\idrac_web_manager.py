#!/usr/bin/env python3

"""
iDRAC Web管理工具
支持通过浏览器自动化操作iDRAC界面
"""

import os
import sys
import time
import argparse
import subprocess
import webbrowser
from urllib.parse import urlparse
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class iDRACWebManager:
    def __init__(self, host, username='root', password='calvin'):
        self.host = host
        self.username = username
        self.password = password
        self.base_url = f"https://{host}"
        self.session = requests.Session()
        self.session.verify = False
        
    def check_connectivity(self):
        """检查iDRAC连接性"""
        try:
            response = self.session.get(f"{self.base_url}/login.html", timeout=10)
            if response.status_code == 200:
                print(f"✓ iDRAC {self.host} 连接正常")
                return True
            else:
                print(f"✗ iDRAC {self.host} 响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ 无法连接到 iDRAC {self.host}: {e}")
            return False
    
    def open_web_interface(self):
        """打开iDRAC Web界面"""
        print(f"正在打开 iDRAC Web界面: {self.base_url}")
        
        # 尝试不同的浏览器
        browsers = [
            'firefox',
            'google-chrome',
            'chromium-browser',
            'microsoft-edge'
        ]
        
        for browser in browsers:
            try:
                if subprocess.run(['which', browser], capture_output=True).returncode == 0:
                    print(f"使用 {browser} 打开Web界面...")
                    subprocess.Popen([browser, self.base_url])
                    return True
            except Exception:
                continue
        
        # 如果没有找到浏览器，使用Python的webbrowser模块
        try:
            webbrowser.open(self.base_url)
            print("使用默认浏览器打开Web界面...")
            return True
        except Exception as e:
            print(f"无法打开浏览器: {e}")
            print(f"请手动打开浏览器访问: {self.base_url}")
            return False
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # 尝试获取系统信息（需要登录）
            login_data = {
                'user': self.username,
                'password': self.password
            }
            
            # 这里可以扩展更多的API调用
            print(f"iDRAC主机: {self.host}")
            print(f"用户名: {self.username}")
            print(f"Web界面: {self.base_url}")
            
        except Exception as e:
            print(f"获取系统信息失败: {e}")
    
    def launch_virtual_console(self):
        """启动虚拟控制台"""
        console_url = f"{self.base_url}/console"
        print(f"启动虚拟控制台: {console_url}")
        
        try:
            webbrowser.open(console_url)
        except Exception as e:
            print(f"无法打开虚拟控制台: {e}")
            print(f"请在Web界面中手动访问虚拟控制台")
    
    def show_common_operations(self):
        """显示常用操作指南"""
        print("\n=== iDRAC 常用操作指南 ===")
        print(f"1. Web界面登录: {self.base_url}")
        print(f"   用户名: {self.username}")
        print(f"   密码: {self.password}")
        print()
        print("2. 常用功能:")
        print("   - 系统概览: Dashboard")
        print("   - 虚拟控制台: Console -> Virtual Console")
        print("   - 虚拟媒体: Console -> Virtual Media")
        print("   - 电源管理: System -> Power/Thermal")
        print("   - BIOS设置: System -> BIOS")
        print("   - 固件更新: Maintenance -> System Update")
        print()
        print("3. 虚拟媒体挂载:")
        print("   - 支持ISO镜像挂载")
        print("   - 支持软盘镜像挂载")
        print("   - 可用于操作系统安装")
        print()
        print("4. KVM集成:")
        print("   - 可以通过iDRAC控制物理服务器")
        print("   - 在KVM虚拟机中嵌套运行")
        print("   - 用于裸机服务器管理")

def create_desktop_shortcut(host, username, password):
    """创建桌面快捷方式"""
    desktop_path = os.path.expanduser("~/Desktop")
    if not os.path.exists(desktop_path):
        desktop_path = os.path.expanduser("~")
    
    shortcut_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=iDRAC {host}
Comment=Connect to iDRAC {host}
Exec=python3 {os.path.abspath(__file__)} --host {host} --username {username} --open
Icon=network-server
Terminal=false
Categories=Network;System;
"""
    
    shortcut_path = os.path.join(desktop_path, f"idrac_{host.replace('.', '_')}.desktop")
    
    try:
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        os.chmod(shortcut_path, 0o755)
        print(f"桌面快捷方式已创建: {shortcut_path}")
    except Exception as e:
        print(f"创建桌面快捷方式失败: {e}")

def scan_idrac_network(network_range="192.168.1"):
    """扫描网络中的iDRAC设备"""
    print(f"扫描网络 {network_range}.0/24 中的iDRAC设备...")
    
    found_devices = []
    
    for i in range(1, 255):
        ip = f"{network_range}.{i}"
        try:
            response = requests.get(f"https://{ip}/login.html", 
                                  timeout=2, verify=False)
            if response.status_code == 200 and 'idrac' in response.text.lower():
                found_devices.append(ip)
                print(f"✓ 发现iDRAC设备: {ip}")
        except:
            continue
    
    if found_devices:
        print(f"\n发现 {len(found_devices)} 个iDRAC设备:")
        for device in found_devices:
            print(f"  - {device}")
    else:
        print("未发现iDRAC设备")
    
    return found_devices

def main():
    parser = argparse.ArgumentParser(description='iDRAC Web管理工具')
    parser.add_argument('--host', required=True, help='iDRAC IP地址或主机名')
    parser.add_argument('--username', default='root', help='用户名 (默认: root)')
    parser.add_argument('--password', default='calvin', help='密码 (默认: calvin)')
    parser.add_argument('--open', action='store_true', help='直接打开Web界面')
    parser.add_argument('--console', action='store_true', help='打开虚拟控制台')
    parser.add_argument('--info', action='store_true', help='显示系统信息')
    parser.add_argument('--shortcut', action='store_true', help='创建桌面快捷方式')
    parser.add_argument('--scan', help='扫描网络中的iDRAC设备 (例如: 192.168.1)')
    
    args = parser.parse_args()
    
    if args.scan:
        scan_idrac_network(args.scan)
        return
    
    # 创建iDRAC管理器实例
    idrac = iDRACWebManager(args.host, args.username, args.password)
    
    # 检查连接性
    if not idrac.check_connectivity():
        sys.exit(1)
    
    # 执行操作
    if args.open:
        idrac.open_web_interface()
    elif args.console:
        idrac.launch_virtual_console()
    elif args.info:
        idrac.get_system_info()
    elif args.shortcut:
        create_desktop_shortcut(args.host, args.username, args.password)
    else:
        # 默认操作：显示信息并打开Web界面
        idrac.get_system_info()
        idrac.show_common_operations()
        
        response = input("\n是否打开Web界面? (y/N): ")
        if response.lower() in ['y', 'yes']:
            idrac.open_web_interface()

if __name__ == "__main__":
    main()
