#!/usr/bin/env python3

"""
iDRAC Web管理工具
支持通过浏览器自动化操作iDRAC界面
"""

import os
import sys
import time
import argparse
import subprocess
import webbrowser
import json
import base64
from urllib.parse import urlparse
import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class iDRACWebManager:
    def __init__(self, host, username='root', password='calvin'):
        self.host = host
        self.username = username
        self.password = password
        self.base_url = f"https://{host}"
        self.session = requests.Session()
        self.session.verify = False
        
    def check_connectivity(self):
        """检查iDRAC连接性"""
        try:
            response = self.session.get(f"{self.base_url}/login.html", timeout=10)
            if response.status_code == 200:
                print(f"✓ iDRAC {self.host} 连接正常")
                return True
            else:
                print(f"✗ iDRAC {self.host} 响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ 无法连接到 iDRAC {self.host}: {e}")
            return False
    
    def open_web_interface(self):
        """打开iDRAC Web界面"""
        print(f"正在打开 iDRAC Web界面: {self.base_url}")
        
        # 尝试不同的浏览器
        browsers = [
            'firefox',
            'google-chrome',
            'chromium-browser',
            'microsoft-edge'
        ]
        
        for browser in browsers:
            try:
                if subprocess.run(['which', browser], capture_output=True).returncode == 0:
                    print(f"使用 {browser} 打开Web界面...")
                    subprocess.Popen([browser, self.base_url])
                    return True
            except Exception:
                continue
        
        # 如果没有找到浏览器，使用Python的webbrowser模块
        try:
            webbrowser.open(self.base_url)
            print("使用默认浏览器打开Web界面...")
            return True
        except Exception as e:
            print(f"无法打开浏览器: {e}")
            print(f"请手动打开浏览器访问: {self.base_url}")
            return False
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # 尝试获取系统信息（需要登录）
            login_data = {
                'user': self.username,
                'password': self.password
            }
            
            # 这里可以扩展更多的API调用
            print(f"iDRAC主机: {self.host}")
            print(f"用户名: {self.username}")
            print(f"Web界面: {self.base_url}")
            
        except Exception as e:
            print(f"获取系统信息失败: {e}")
    
    def launch_virtual_console(self):
        """启动虚拟控制台"""
        console_url = f"{self.base_url}/console"
        print(f"启动虚拟控制台: {console_url}")
        
        try:
            webbrowser.open(console_url)
        except Exception as e:
            print(f"无法打开虚拟控制台: {e}")
            print(f"请在Web界面中手动访问虚拟控制台")
    
    def setup_webdriver(self, headless=False):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--ignore-ssl-errors")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-web-security")

            # 尝试使用Chrome
            try:
                driver = webdriver.Chrome(options=chrome_options)
                return driver
            except:
                # 如果Chrome不可用，尝试Firefox
                from selenium.webdriver.firefox.options import Options as FirefoxOptions
                firefox_options = FirefoxOptions()
                if headless:
                    firefox_options.add_argument("--headless")
                driver = webdriver.Firefox(options=firefox_options)
                return driver
        except Exception as e:
            print(f"无法启动WebDriver: {e}")
            print("请确保已安装Chrome/Firefox和对应的WebDriver")
            return None

    def login_idrac(self, driver):
        """自动登录iDRAC"""
        try:
            print("正在登录iDRAC...")
            driver.get(self.base_url)

            # 等待登录页面加载
            wait = WebDriverWait(driver, 10)

            # 查找用户名输入框
            username_field = wait.until(EC.presence_of_element_located((By.ID, "user")))
            username_field.clear()
            username_field.send_keys(self.username)

            # 查找密码输入框
            password_field = driver.find_element(By.ID, "password")
            password_field.clear()
            password_field.send_keys(self.password)

            # 点击登录按钮
            login_button = driver.find_element(By.ID, "btnOK")
            login_button.click()

            # 等待登录完成
            wait.until(EC.url_changes(f"{self.base_url}/login.html"))
            print("✓ 登录成功")
            return True

        except Exception as e:
            print(f"✗ 登录失败: {e}")
            return False

    def mount_iso_virtual_media(self, driver, iso_path):
        """挂载ISO到虚拟媒体"""
        try:
            print(f"正在挂载ISO: {iso_path}")

            # 导航到虚拟媒体页面
            driver.get(f"{self.base_url}/console")

            wait = WebDriverWait(driver, 10)

            # 点击虚拟媒体选项卡
            virtual_media_tab = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Virtual Media")))
            virtual_media_tab.click()

            # 选择CD/DVD设备
            cd_device = wait.until(EC.element_to_be_clickable((By.ID, "cdDevice")))
            cd_device.click()

            # 选择映像文件
            browse_button = driver.find_element(By.ID, "browseButton")
            browse_button.click()

            # 输入ISO路径
            file_input = driver.find_element(By.ID, "fileInput")
            file_input.send_keys(iso_path)

            # 点击连接按钮
            connect_button = driver.find_element(By.ID, "connectButton")
            connect_button.click()

            print("✓ ISO挂载成功")
            return True

        except Exception as e:
            print(f"✗ ISO挂载失败: {e}")
            return False

    def power_control(self, driver, action="reset"):
        """电源控制"""
        try:
            print(f"正在执行电源操作: {action}")

            # 导航到电源控制页面
            driver.get(f"{self.base_url}/system")

            wait = WebDriverWait(driver, 10)

            # 点击电源控制
            power_control = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Power/Thermal")))
            power_control.click()

            # 根据操作类型选择相应按钮
            if action == "reset":
                power_button = driver.find_element(By.ID, "resetButton")
            elif action == "power_on":
                power_button = driver.find_element(By.ID, "powerOnButton")
            elif action == "power_off":
                power_button = driver.find_element(By.ID, "powerOffButton")
            else:
                print(f"不支持的电源操作: {action}")
                return False

            power_button.click()

            # 确认操作
            confirm_button = wait.until(EC.element_to_be_clickable((By.ID, "confirmButton")))
            confirm_button.click()

            print(f"✓ 电源操作 {action} 执行成功")
            return True

        except Exception as e:
            print(f"✗ 电源操作失败: {e}")
            return False

    def auto_install_kvm_iso(self, iso_path, boot_from_cd=True):
        """自动安装KVM虚拟化ISO"""
        print(f"开始自动安装KVM虚拟化系统...")
        print(f"ISO路径: {iso_path}")

        driver = self.setup_webdriver(headless=False)
        if not driver:
            return False

        try:
            # 1. 登录iDRAC
            if not self.login_idrac(driver):
                return False

            # 2. 挂载ISO
            if not self.mount_iso_virtual_media(driver, iso_path):
                return False

            # 3. 设置从CD启动（如果需要）
            if boot_from_cd:
                self.set_boot_order(driver, "cd")

            # 4. 重启服务器
            if not self.power_control(driver, "reset"):
                return False

            # 5. 打开虚拟控制台进行安装监控
            self.open_virtual_console(driver)

            print("✓ 自动安装流程启动成功")
            print("请在虚拟控制台中监控安装进度")

            return True

        except Exception as e:
            print(f"✗ 自动安装失败: {e}")
            return False
        finally:
            # 保持浏览器打开以便监控安装
            input("按Enter键关闭浏览器...")
            driver.quit()

    def set_boot_order(self, driver, boot_device="cd"):
        """设置启动顺序"""
        try:
            print(f"设置启动设备: {boot_device}")

            # 导航到BIOS设置页面
            driver.get(f"{self.base_url}/system/bios")

            wait = WebDriverWait(driver, 10)

            # 这里需要根据具体的iDRAC版本调整选择器
            # 不同版本的iDRAC界面可能不同
            boot_settings = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Boot Settings")))
            boot_settings.click()

            if boot_device == "cd":
                cd_option = driver.find_element(By.ID, "bootFromCD")
                cd_option.click()

            # 应用设置
            apply_button = driver.find_element(By.ID, "applyButton")
            apply_button.click()

            print("✓ 启动顺序设置成功")
            return True

        except Exception as e:
            print(f"✗ 启动顺序设置失败: {e}")
            return False

    def open_virtual_console(self, driver):
        """打开虚拟控制台"""
        try:
            print("打开虚拟控制台...")

            # 导航到控制台页面
            driver.get(f"{self.base_url}/console")

            wait = WebDriverWait(driver, 10)

            # 点击虚拟控制台
            console_button = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Launch Virtual Console")))
            console_button.click()

            print("✓ 虚拟控制台已打开")
            return True

        except Exception as e:
            print(f"✗ 虚拟控制台打开失败: {e}")
            return False

    def show_common_operations(self):
        """显示常用操作指南"""
        print("\n=== iDRAC 常用操作指南 ===")
        print(f"1. Web界面登录: {self.base_url}")
        print(f"   用户名: {self.username}")
        print(f"   密码: {self.password}")
        print()
        print("2. 常用功能:")
        print("   - 系统概览: Dashboard")
        print("   - 虚拟控制台: Console -> Virtual Console")
        print("   - 虚拟媒体: Console -> Virtual Media")
        print("   - 电源管理: System -> Power/Thermal")
        print("   - BIOS设置: System -> BIOS")
        print("   - 固件更新: Maintenance -> System Update")
        print()
        print("3. 虚拟媒体挂载:")
        print("   - 支持ISO镜像挂载")
        print("   - 支持软盘镜像挂载")
        print("   - 可用于操作系统安装")
        print()
        print("4. KVM集成:")
        print("   - 可以通过iDRAC控制物理服务器")
        print("   - 在KVM虚拟机中嵌套运行")
        print("   - 用于裸机服务器管理")

def create_desktop_shortcut(host, username, password):
    """创建桌面快捷方式"""
    desktop_path = os.path.expanduser("~/Desktop")
    if not os.path.exists(desktop_path):
        desktop_path = os.path.expanduser("~")
    
    shortcut_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=iDRAC {host}
Comment=Connect to iDRAC {host}
Exec=python3 {os.path.abspath(__file__)} --host {host} --username {username} --open
Icon=network-server
Terminal=false
Categories=Network;System;
"""
    
    shortcut_path = os.path.join(desktop_path, f"idrac_{host.replace('.', '_')}.desktop")
    
    try:
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        os.chmod(shortcut_path, 0o755)
        print(f"桌面快捷方式已创建: {shortcut_path}")
    except Exception as e:
        print(f"创建桌面快捷方式失败: {e}")

def scan_idrac_network(network_range="192.168.1"):
    """扫描网络中的iDRAC设备"""
    print(f"扫描网络 {network_range}.0/24 中的iDRAC设备...")
    
    found_devices = []
    
    for i in range(1, 255):
        ip = f"{network_range}.{i}"
        try:
            response = requests.get(f"https://{ip}/login.html", 
                                  timeout=2, verify=False)
            if response.status_code == 200 and 'idrac' in response.text.lower():
                found_devices.append(ip)
                print(f"✓ 发现iDRAC设备: {ip}")
        except:
            continue
    
    if found_devices:
        print(f"\n发现 {len(found_devices)} 个iDRAC设备:")
        for device in found_devices:
            print(f"  - {device}")
    else:
        print("未发现iDRAC设备")
    
    return found_devices

def main():
    parser = argparse.ArgumentParser(description='iDRAC Web管理工具 - 支持自动安装KVM虚拟化系统')
    parser.add_argument('--host', required=True, help='iDRAC IP地址或主机名')
    parser.add_argument('--username', default='root', help='用户名 (默认: root)')
    parser.add_argument('--password', default='calvin', help='密码 (默认: calvin)')
    parser.add_argument('--open', action='store_true', help='直接打开Web界面')
    parser.add_argument('--console', action='store_true', help='打开虚拟控制台')
    parser.add_argument('--info', action='store_true', help='显示系统信息')
    parser.add_argument('--shortcut', action='store_true', help='创建桌面快捷方式')
    parser.add_argument('--scan', help='扫描网络中的iDRAC设备 (例如: 192.168.1)')
    parser.add_argument('--install-kvm', help='自动安装KVM虚拟化系统，指定ISO文件路径')
    parser.add_argument('--iso-path', help='ISO文件路径（用于--install-kvm）')
    parser.add_argument('--mount-iso', help='仅挂载ISO文件到虚拟媒体')
    parser.add_argument('--power', choices=['on', 'off', 'reset'], help='电源控制操作')

    args = parser.parse_args()

    if args.scan:
        scan_idrac_network(args.scan)
        return

    # 创建iDRAC管理器实例
    idrac = iDRACWebManager(args.host, args.username, args.password)

    # 检查连接性
    if not idrac.check_connectivity():
        sys.exit(1)

    # 执行操作
    if args.install_kvm:
        iso_path = args.iso_path or args.install_kvm
        if not os.path.exists(iso_path):
            print(f"错误: ISO文件不存在: {iso_path}")
            sys.exit(1)

        print(f"准备自动安装KVM虚拟化系统...")
        print(f"iDRAC: {args.host}")
        print(f"ISO: {iso_path}")

        confirm = input("确认开始自动安装? (y/N): ")
        if confirm.lower() in ['y', 'yes']:
            idrac.auto_install_kvm_iso(iso_path)
        else:
            print("安装已取消")

    elif args.mount_iso:
        if not os.path.exists(args.mount_iso):
            print(f"错误: ISO文件不存在: {args.mount_iso}")
            sys.exit(1)

        driver = idrac.setup_webdriver()
        if driver and idrac.login_idrac(driver):
            idrac.mount_iso_virtual_media(driver, args.mount_iso)
            input("按Enter键关闭浏览器...")
            driver.quit()

    elif args.power:
        driver = idrac.setup_webdriver()
        if driver and idrac.login_idrac(driver):
            action_map = {'on': 'power_on', 'off': 'power_off', 'reset': 'reset'}
            idrac.power_control(driver, action_map[args.power])
            driver.quit()

    elif args.open:
        idrac.open_web_interface()
    elif args.console:
        idrac.launch_virtual_console()
    elif args.info:
        idrac.get_system_info()
    elif args.shortcut:
        create_desktop_shortcut(args.host, args.username, args.password)
    else:
        # 默认操作：显示信息并打开Web界面
        idrac.get_system_info()
        idrac.show_common_operations()

        print("\n可用操作:")
        print("1. 打开Web界面")
        print("2. 自动安装KVM虚拟化系统")
        print("3. 挂载ISO文件")
        print("4. 电源控制")

        choice = input("\n请选择操作 (1-4) 或按Enter打开Web界面: ")

        if choice == "1" or choice == "":
            idrac.open_web_interface()
        elif choice == "2":
            iso_path = input("请输入KVM ISO文件路径: ")
            if os.path.exists(iso_path):
                idrac.auto_install_kvm_iso(iso_path)
            else:
                print(f"ISO文件不存在: {iso_path}")
        elif choice == "3":
            iso_path = input("请输入ISO文件路径: ")
            if os.path.exists(iso_path):
                driver = idrac.setup_webdriver()
                if driver and idrac.login_idrac(driver):
                    idrac.mount_iso_virtual_media(driver, iso_path)
                    input("按Enter键关闭浏览器...")
                    driver.quit()
            else:
                print(f"ISO文件不存在: {iso_path}")
        elif choice == "4":
            print("电源控制选项:")
            print("1. 开机")
            print("2. 关机")
            print("3. 重启")
            power_choice = input("请选择 (1-3): ")

            power_map = {"1": "power_on", "2": "power_off", "3": "reset"}
            if power_choice in power_map:
                driver = idrac.setup_webdriver()
                if driver and idrac.login_idrac(driver):
                    idrac.power_control(driver, power_map[power_choice])
                    driver.quit()

if __name__ == "__main__":
    main()
