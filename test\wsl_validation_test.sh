#!/bin/bash

# WSL环境验证测试脚本
# 作者: Augment Agent
# 日期: $(date)

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 运行测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "运行测试: $test_name"
    
    if eval "$test_command"; then
        log_success "✓ $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "✗ $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 基础环境检查
test_basic_environment() {
    log_info "=== 基础环境检查 ==="
    
    # 检查WSL环境
    run_test "WSL环境检查" "grep -q Microsoft /proc/version"
    
    # 检查基本命令
    run_test "bash可用性" "command -v bash > /dev/null"
    run_test "curl可用性" "command -v curl > /dev/null"
    run_test "git可用性" "command -v git > /dev/null"
    
    # 检查网络连接
    run_test "网络连接" "ping -c 1 ******* > /dev/null 2>&1"
    
    # 检查磁盘空间
    run_test "磁盘空间检查" "[[ \$(df / | tail -1 | awk '{print \$5}' | sed 's/%//') -lt 90 ]]"
}

# 开发环境检查
test_development_environment() {
    log_info "=== 开发环境检查 ==="
    
    # 检查常用开发工具
    if command -v python3 > /dev/null; then
        run_test "Python3版本" "python3 --version"
        run_test "pip可用性" "command -v pip3 > /dev/null"
    else
        log_warning "Python3未安装"
    fi
    
    if command -v node > /dev/null; then
        run_test "Node.js版本" "node --version"
        run_test "npm可用性" "command -v npm > /dev/null"
    else
        log_warning "Node.js未安装"
    fi
    
    if command -v docker > /dev/null; then
        run_test "Docker可用性" "docker --version"
        run_test "Docker服务状态" "docker info > /dev/null 2>&1"
    else
        log_warning "Docker未安装"
    fi
}

# 项目特定测试
test_project_specific() {
    log_info "=== 项目特定测试 ==="
    
    # 检查项目目录结构
    run_test "项目根目录存在" "[[ -d \$(pwd) ]]"
    
    # 如果存在package.json，检查Node.js项目
    if [[ -f "package.json" ]]; then
        log_info "检测到Node.js项目"
        run_test "package.json有效" "node -e 'JSON.parse(require(\"fs\").readFileSync(\"package.json\"))'"
        
        if [[ -f "package-lock.json" ]] || [[ -d "node_modules" ]]; then
            run_test "依赖安装检查" "[[ -d node_modules ]]"
        fi
    fi
    
    # 如果存在requirements.txt，检查Python项目
    if [[ -f "requirements.txt" ]]; then
        log_info "检测到Python项目"
        run_test "requirements.txt可读" "[[ -r requirements.txt ]]"
    fi
    
    # 如果存在Dockerfile，检查Docker项目
    if [[ -f "Dockerfile" ]]; then
        log_info "检测到Docker项目"
        run_test "Dockerfile可读" "[[ -r Dockerfile ]]"
    fi
    
    # 检查测试目录
    if [[ -d "test" ]] || [[ -d "tests" ]]; then
        run_test "测试目录存在" "[[ -d test ]] || [[ -d tests ]]"
    fi
}

# 自定义测试函数（用户可以在这里添加特定的测试）
test_custom() {
    log_info "=== 自定义测试 ==="
    
    # 示例：测试特定端口是否可用
    # run_test "端口3000可用" "! nc -z localhost 3000"
    
    # 示例：测试特定文件是否存在
    # run_test "配置文件存在" "[[ -f config/app.conf ]]"
    
    # 示例：测试环境变量
    # run_test "环境变量设置" "[[ -n \$MY_APP_ENV ]]"
    
    log_info "请在test_custom函数中添加您的特定测试"
}

# 生成测试报告
generate_report() {
    log_info "=== 测试报告 ==="
    echo "总测试数: $TOTAL_TESTS"
    echo "通过: $PASSED_TESTS"
    echo "失败: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "所有测试通过！"
        exit 0
    else
        log_error "有 $FAILED_TESTS 个测试失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始WSL环境验证测试..."
    echo "测试时间: $(date)"
    echo "当前目录: $(pwd)"
    echo "用户: $(whoami)"
    echo "系统信息: $(uname -a)"
    echo ""
    
    test_basic_environment
    echo ""
    
    test_development_environment
    echo ""
    
    test_project_specific
    echo ""
    
    test_custom
    echo ""
    
    generate_report
}

# 检查是否直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
