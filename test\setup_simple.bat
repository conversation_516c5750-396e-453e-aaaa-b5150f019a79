@echo off
title Setup iDRAC Automation Tools

echo ========================================
echo   Setup iDRAC Automation Tools
echo ========================================
echo.

REM Check Python
echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.6+ from https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo OK: Python is installed
    python --version
)

REM Check pip
echo.
echo Checking pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip not available
    pause
    exit /b 1
) else (
    echo OK: pip is available
)

REM Install packages
echo.
echo Installing required packages...
echo.

echo Installing requests...
pip install requests

echo Installing selenium...
pip install selenium

echo Installing webdriver-manager...
pip install webdriver-manager

echo.
echo Testing installation...
python -c "import requests; import selenium; print('OK: All packages installed successfully')"

if errorlevel 1 (
    echo ERROR: Package installation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo           Setup Complete!
echo ========================================
echo.
echo You can now use:
echo 1. test\install_kvm_via_idrac.bat
echo 2. python test\idrac_web_manager.py --help
echo.

pause
