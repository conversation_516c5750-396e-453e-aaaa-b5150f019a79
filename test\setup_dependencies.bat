@echo off
title Install iDRAC Automation Tool Dependencies

echo ========================================
echo   Install iDRAC Automation Tool Dependencies
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo 注意: 某些操作可能需要管理员权限
    echo 如果遇到权限问题，请以管理员身份运行此脚本
    echo.
)

REM 检查Python是否安装
echo 检查Python安装...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python
    echo.
    echo 请先安装Python 3.6或更高版本:
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载最新版本的Python
    echo 3. 安装时勾选 "Add Python to PATH"
    echo 4. 重新运行此脚本
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Python已安装
    python --version
)

REM 检查pip
echo.
echo 检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误: pip不可用
    echo 请重新安装Python并确保包含pip
    pause
    exit /b 1
) else (
    echo ✓ pip可用
)

REM 升级pip
echo.
echo 升级pip到最新版本...
python -m pip install --upgrade pip

REM 安装必要的Python包
echo.
echo 安装必要的Python包...
echo.

echo 安装 requests (用于HTTP请求)...
pip install requests

echo 安装 selenium (用于浏览器自动化)...
pip install selenium

echo 安装 webdriver-manager (自动管理WebDriver)...
pip install webdriver-manager

echo.
echo 检查浏览器和WebDriver...

REM 检查Chrome
where chrome >nul 2>&1
if not errorlevel 1 (
    echo ✓ 检测到Chrome浏览器
    set CHROME_FOUND=1
) else (
    echo ⚠ 未检测到Chrome浏览器
    set CHROME_FOUND=0
)

REM 检查Firefox
where firefox >nul 2>&1
if not errorlevel 1 (
    echo ✓ 检测到Firefox浏览器
    set FIREFOX_FOUND=1
) else (
    echo ⚠ 未检测到Firefox浏览器
    set FIREFOX_FOUND=0
)

REM 检查Edge
where msedge >nul 2>&1
if not errorlevel 1 (
    echo ✓ 检测到Microsoft Edge浏览器
    set EDGE_FOUND=1
) else (
    echo ⚠ 未检测到Microsoft Edge浏览器
    set EDGE_FOUND=0
)

echo.
if "%CHROME_FOUND%"=="0" if "%FIREFOX_FOUND%"=="0" if "%EDGE_FOUND%"=="0" (
    echo 警告: 未检测到支持的浏览器
    echo 请安装以下浏览器之一:
    echo - Google Chrome: https://www.google.com/chrome/
    echo - Mozilla Firefox: https://www.mozilla.org/firefox/
    echo - Microsoft Edge: 通常已预装在Windows 10/11中
    echo.
)

REM 下载ChromeDriver (如果需要)
if "%CHROME_FOUND%"=="1" (
    echo 配置ChromeDriver...
    python -c "from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()" 2>nul
    if not errorlevel 1 (
        echo ✓ ChromeDriver配置完成
    ) else (
        echo ⚠ ChromeDriver配置可能失败，但程序仍可运行
    )
)

echo.
echo 创建测试脚本...

REM 创建简单的测试脚本
echo import requests > test_connection.py
echo import sys >> test_connection.py
echo. >> test_connection.py
echo try: >> test_connection.py
echo     from selenium import webdriver >> test_connection.py
echo     print("✓ Selenium导入成功") >> test_connection.py
echo except ImportError: >> test_connection.py
echo     print("✗ Selenium导入失败") >> test_connection.py
echo     sys.exit(1) >> test_connection.py
echo. >> test_connection.py
echo try: >> test_connection.py
echo     from webdriver_manager.chrome import ChromeDriverManager >> test_connection.py
echo     print("✓ WebDriver Manager可用") >> test_connection.py
echo except ImportError: >> test_connection.py
echo     print("⚠ WebDriver Manager不可用，但不影响基本功能") >> test_connection.py
echo. >> test_connection.py
echo print("✓ 所有依赖检查完成") >> test_connection.py

echo 运行依赖测试...
python test_connection.py
if errorlevel 1 (
    echo.
    echo 错误: 依赖测试失败
    echo 请检查上述错误信息并重新安装相关包
    pause
    exit /b 1
)

REM 清理测试文件
del test_connection.py >nul 2>&1

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo.
echo 已安装的组件:
echo ✓ Python包: requests, selenium, webdriver-manager
echo ✓ 浏览器自动化支持
echo.
echo 现在您可以使用以下命令:
echo.
echo 1. 运行自动安装工具:
echo    install_kvm_via_idrac.bat
echo.
echo 2. 直接使用Python脚本:
echo    python test\idrac_web_manager.py --host ************* --install-kvm "C:\path\to\kvm.iso"
echo.
echo 3. 扫描网络中的iDRAC设备:
echo    python test\idrac_web_manager.py --scan 192.168.1
echo.

pause
