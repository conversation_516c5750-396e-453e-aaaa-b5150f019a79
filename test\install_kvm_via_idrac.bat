@echo off
chcp 65001 >nul
title iDRAC自动安装KVM虚拟化系统

echo ========================================
echo    iDRAC自动安装KVM虚拟化系统工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误: pip不可用，请检查Python安装
    pause
    exit /b 1
)

echo 检查并安装必要的Python包...
echo.

REM 安装必要的包
pip install requests selenium >nul 2>&1
if errorlevel 1 (
    echo 警告: 某些包安装可能失败，但会尝试继续运行
)

echo 安装完成！
echo.

REM 获取用户输入
set /p IDRAC_HOST="请输入iDRAC IP地址: "
if "%IDRAC_HOST%"=="" (
    echo 错误: 必须提供iDRAC IP地址
    pause
    exit /b 1
)

set /p IDRAC_USER="请输入用户名 [默认: root]: "
if "%IDRAC_USER%"=="" set IDRAC_USER=root

set /p IDRAC_PASS="请输入密码 [默认: calvin]: "
if "%IDRAC_PASS%"=="" set IDRAC_PASS=calvin

echo.
echo 请选择操作:
echo 1. 自动安装KVM虚拟化系统 (需要ISO文件)
echo 2. 仅挂载ISO文件
echo 3. 打开iDRAC Web界面
echo 4. 电源控制
echo 5. 扫描网络中的iDRAC设备
echo.

set /p CHOICE="请选择 (1-5): "

if "%CHOICE%"=="1" goto INSTALL_KVM
if "%CHOICE%"=="2" goto MOUNT_ISO
if "%CHOICE%"=="3" goto OPEN_WEB
if "%CHOICE%"=="4" goto POWER_CONTROL
if "%CHOICE%"=="5" goto SCAN_NETWORK

echo 无效选择，默认打开Web界面
goto OPEN_WEB

:INSTALL_KVM
echo.
echo 请选择KVM虚拟化ISO:
echo 1. Ubuntu Server (推荐用于KVM)
echo 2. CentOS/RHEL
echo 3. 自定义ISO文件路径
echo.

set /p ISO_CHOICE="请选择 (1-3): "

if "%ISO_CHOICE%"=="1" (
    echo 请下载Ubuntu Server ISO文件
    echo 下载地址: https://ubuntu.com/download/server
    set /p ISO_PATH="请输入Ubuntu Server ISO文件完整路径: "
) else if "%ISO_CHOICE%"=="2" (
    echo 请下载CentOS/RHEL ISO文件
    echo CentOS下载: https://www.centos.org/download/
    echo RHEL下载: https://access.redhat.com/downloads/
    set /p ISO_PATH="请输入CentOS/RHEL ISO文件完整路径: "
) else if "%ISO_CHOICE%"=="3" (
    set /p ISO_PATH="请输入ISO文件完整路径: "
) else (
    echo 无效选择
    pause
    exit /b 1
)

if not exist "%ISO_PATH%" (
    echo 错误: ISO文件不存在: %ISO_PATH%
    pause
    exit /b 1
)

echo.
echo 准备自动安装KVM虚拟化系统...
echo iDRAC: %IDRAC_HOST%
echo 用户: %IDRAC_USER%
echo ISO: %ISO_PATH%
echo.

set /p CONFIRM="确认开始自动安装? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo 启动自动安装...
python test\idrac_web_manager.py --host %IDRAC_HOST% --username %IDRAC_USER% --password %IDRAC_PASS% --install-kvm "%ISO_PATH%"
goto END

:MOUNT_ISO
set /p ISO_PATH="请输入要挂载的ISO文件路径: "
if not exist "%ISO_PATH%" (
    echo 错误: ISO文件不存在: %ISO_PATH%
    pause
    exit /b 1
)

echo 挂载ISO文件...
python test\idrac_web_manager.py --host %IDRAC_HOST% --username %IDRAC_USER% --password %IDRAC_PASS% --mount-iso "%ISO_PATH%"
goto END

:OPEN_WEB
echo 打开iDRAC Web界面...
python test\idrac_web_manager.py --host %IDRAC_HOST% --username %IDRAC_USER% --password %IDRAC_PASS% --open
goto END

:POWER_CONTROL
echo.
echo 电源控制选项:
echo 1. 开机
echo 2. 关机
echo 3. 重启
echo.

set /p POWER_CHOICE="请选择 (1-3): "

if "%POWER_CHOICE%"=="1" (
    set POWER_ACTION=on
) else if "%POWER_CHOICE%"=="2" (
    set POWER_ACTION=off
) else if "%POWER_CHOICE%"=="3" (
    set POWER_ACTION=reset
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo 执行电源操作: %POWER_ACTION%
python test\idrac_web_manager.py --host %IDRAC_HOST% --username %IDRAC_USER% --password %IDRAC_PASS% --power %POWER_ACTION%
goto END

:SCAN_NETWORK
set /p NETWORK="请输入网络段 [默认: 192.168.1]: "
if "%NETWORK%"=="" set NETWORK=192.168.1

echo 扫描网络 %NETWORK%.0/24 中的iDRAC设备...
python test\idrac_web_manager.py --scan %NETWORK%
goto END

:END
echo.
echo 操作完成！
pause
