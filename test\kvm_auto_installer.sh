#!/bin/bash

# KVM虚拟化环境自动安装脚本
# 支持Ubuntu/Debian和CentOS/RHEL系统
# 包含iDRAC等远程管理工具配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查CPU虚拟化支持
check_virtualization_support() {
    log_info "检查CPU虚拟化支持..."
    
    if grep -E '(vmx|svm)' /proc/cpuinfo > /dev/null; then
        log_success "CPU支持硬件虚拟化"
    else
        log_error "CPU不支持硬件虚拟化，请在BIOS中启用VT-x/AMD-V"
        exit 1
    fi
    
    # 检查嵌套虚拟化（如果在虚拟机中运行）
    if [[ -f /sys/module/kvm_intel/parameters/nested ]] || [[ -f /sys/module/kvm_amd/parameters/nested ]]; then
        log_info "检测到可能的嵌套虚拟化环境"
    fi
}

# Ubuntu/Debian安装函数
install_kvm_ubuntu() {
    log_info "在Ubuntu/Debian系统上安装KVM..."
    
    # 更新包列表
    sudo apt update
    
    # 安装KVM和相关工具
    sudo apt install -y \
        qemu-kvm \
        libvirt-daemon-system \
        libvirt-clients \
        bridge-utils \
        virt-manager \
        virt-viewer \
        virt-top \
        libguestfs-tools \
        genisoimage \
        virtinst \
        cpu-checker
    
    # 安装浏览器和远程管理工具
    sudo apt install -y \
        firefox \
        remmina \
        remmina-plugin-vnc \
        remmina-plugin-rdp \
        tigervnc-viewer \
        openssh-client \
        curl \
        wget
    
    log_success "Ubuntu/Debian KVM安装完成"
}

# CentOS/RHEL安装函数
install_kvm_centos() {
    log_info "在CentOS/RHEL系统上安装KVM..."
    
    # 安装EPEL仓库
    sudo yum install -y epel-release
    
    # 安装KVM和相关工具
    sudo yum groupinstall -y "Virtualization Host"
    sudo yum install -y \
        qemu-kvm \
        libvirt \
        libvirt-python \
        libguestfs-tools \
        virt-install \
        virt-viewer \
        virt-top \
        bridge-utils
    
    # 安装浏览器和远程管理工具
    sudo yum install -y \
        firefox \
        tigervnc \
        openssh-clients \
        curl \
        wget
    
    log_success "CentOS/RHEL KVM安装完成"
}

# 配置用户权限
configure_user_permissions() {
    log_info "配置用户权限..."
    
    # 将当前用户添加到libvirt和kvm组
    sudo usermod -aG libvirt $(whoami)
    sudo usermod -aG kvm $(whoami)
    
    # 如果存在libvirt-qemu组，也添加进去
    if getent group libvirt-qemu > /dev/null 2>&1; then
        sudo usermod -aG libvirt-qemu $(whoami)
    fi
    
    log_success "用户权限配置完成"
    log_warning "请注销并重新登录以使组权限生效"
}

# 启动并启用服务
start_services() {
    log_info "启动KVM相关服务..."
    
    sudo systemctl enable libvirtd
    sudo systemctl start libvirtd
    
    # 检查服务状态
    if systemctl is-active --quiet libvirtd; then
        log_success "libvirtd服务运行正常"
    else
        log_error "libvirtd服务启动失败"
        exit 1
    fi
    
    # 启动默认网络
    sudo virsh net-start default 2>/dev/null || true
    sudo virsh net-autostart default 2>/dev/null || true
}

# 配置网络桥接
configure_network_bridge() {
    log_info "配置网络桥接..."
    
    # 创建桥接网络配置
    cat << EOF | sudo tee /tmp/br0.xml > /dev/null
<network>
  <name>br0</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='br0' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='*************00' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF
    
    # 定义并启动桥接网络
    sudo virsh net-define /tmp/br0.xml 2>/dev/null || true
    sudo virsh net-start br0 2>/dev/null || true
    sudo virsh net-autostart br0 2>/dev/null || true
    
    sudo rm -f /tmp/br0.xml
    
    log_success "网络桥接配置完成"
}

# 创建iDRAC访问脚本
create_idrac_tools() {
    log_info "创建iDRAC管理工具..."
    
    # 创建iDRAC连接脚本
    cat << 'EOF' > ~/idrac_connect.sh
#!/bin/bash

# iDRAC连接工具
# 用法: ./idrac_connect.sh <idrac_ip> [username] [password]

IDRAC_IP=$1
USERNAME=${2:-root}
PASSWORD=${3:-calvin}

if [[ -z "$IDRAC_IP" ]]; then
    echo "用法: $0 <idrac_ip> [username] [password]"
    echo "示例: $0 ************* root calvin"
    exit 1
fi

echo "连接到iDRAC: $IDRAC_IP"
echo "用户名: $USERNAME"

# 打开iDRAC Web界面
if command -v firefox > /dev/null; then
    firefox "https://$IDRAC_IP" &
elif command -v google-chrome > /dev/null; then
    google-chrome "https://$IDRAC_IP" &
elif command -v chromium-browser > /dev/null; then
    chromium-browser "https://$IDRAC_IP" &
else
    echo "请手动打开浏览器访问: https://$IDRAC_IP"
fi

# 显示常用iDRAC命令
echo ""
echo "常用iDRAC操作:"
echo "1. Web界面: https://$IDRAC_IP"
echo "2. SSH连接: ssh $USERNAME@$IDRAC_IP"
echo "3. 虚拟控制台: 在Web界面中选择 'Console' -> 'Virtual Console'"
echo "4. 虚拟媒体: 在Web界面中选择 'Virtual Media'"
echo ""
EOF

    chmod +x ~/idrac_connect.sh
    
    # 创建KVM管理脚本
    cat << 'EOF' > ~/kvm_manager.sh
#!/bin/bash

# KVM虚拟机管理工具

show_help() {
    echo "KVM虚拟机管理工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  list        - 列出所有虚拟机"
    echo "  status      - 显示KVM服务状态"
    echo "  networks    - 显示虚拟网络"
    echo "  pools       - 显示存储池"
    echo "  gui         - 启动图形管理界面"
    echo "  help        - 显示此帮助信息"
    echo ""
}

case "$1" in
    list)
        echo "=== 虚拟机列表 ==="
        virsh list --all
        ;;
    status)
        echo "=== KVM服务状态 ==="
        systemctl status libvirtd
        ;;
    networks)
        echo "=== 虚拟网络 ==="
        virsh net-list --all
        ;;
    pools)
        echo "=== 存储池 ==="
        virsh pool-list --all
        ;;
    gui)
        echo "启动图形管理界面..."
        virt-manager &
        ;;
    help|*)
        show_help
        ;;
esac
EOF

    chmod +x ~/kvm_manager.sh
    
    log_success "管理工具创建完成:"
    log_info "  - iDRAC连接工具: ~/idrac_connect.sh"
    log_info "  - KVM管理工具: ~/kvm_manager.sh"
}

# 验证安装
verify_installation() {
    log_info "验证KVM安装..."
    
    # 检查KVM模块
    if lsmod | grep kvm > /dev/null; then
        log_success "KVM内核模块已加载"
    else
        log_error "KVM内核模块未加载"
        return 1
    fi
    
    # 检查libvirt服务
    if systemctl is-active --quiet libvirtd; then
        log_success "libvirtd服务运行正常"
    else
        log_error "libvirtd服务未运行"
        return 1
    fi
    
    # 检查虚拟化能力
    if command -v kvm-ok > /dev/null; then
        kvm-ok
    elif command -v virt-host-validate > /dev/null; then
        virt-host-validate
    fi
    
    log_success "KVM安装验证完成"
}

# 主安装函数
main() {
    log_info "开始KVM虚拟化环境自动安装..."
    
    # 检查是否为root用户
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
    
    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_info "需要sudo权限，请输入密码..."
        sudo -v
    fi
    
    detect_os
    check_virtualization_support
    
    # 根据操作系统选择安装方法
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        install_kvm_ubuntu
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Rocky"* ]]; then
        install_kvm_centos
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    configure_user_permissions
    start_services
    configure_network_bridge
    create_idrac_tools
    verify_installation
    
    log_success "KVM虚拟化环境安装完成！"
    echo ""
    echo "下一步操作:"
    echo "1. 注销并重新登录以使用户组权限生效"
    echo "2. 运行 ~/kvm_manager.sh gui 启动图形管理界面"
    echo "3. 使用 ~/idrac_connect.sh <IP地址> 连接iDRAC"
    echo "4. 运行 ./wsl_validation_test.sh 验证环境"
}

# 运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
