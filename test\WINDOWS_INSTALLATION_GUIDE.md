# Windows环境下通过iDRAC自动安装KVM虚拟化系统

## 🎯 功能概述

这个工具可以让您在Windows电脑上，通过Dell iDRAC远程管理接口，**全自动安装KVM虚拟化系统**到远程服务器上。

### ✨ 主要特性
- 🤖 **完全自动化**: 一键完成从ISO挂载到系统重启的全过程
- 🌐 **浏览器自动化**: 使用Selenium自动操作iDRAC Web界面
- 💿 **智能ISO管理**: 自动挂载ISO文件到虚拟媒体
- ⚡ **电源控制**: 自动重启服务器并设置启动顺序
- 🖥️ **实时监控**: 自动打开虚拟控制台监控安装进度

## 📋 系统要求

### Windows环境
- Windows 10/11 或 Windows Server 2016+
- 管理员权限（用于安装软件包）
- 网络连接到iDRAC设备

### 软件依赖
- Python 3.6+ 
- Chrome/Firefox/Edge浏览器
- 网络访问权限

## 🚀 快速开始

### 步骤1: 安装Python
1. 访问 https://www.python.org/downloads/
2. 下载最新版本的Python
3. 安装时**务必勾选** "Add Python to PATH"
4. 验证安装: 打开命令提示符，输入 `python --version`

### 步骤2: 安装依赖包
```batch
# 运行依赖安装脚本
test\setup_simple.bat
```

或手动安装：
```batch
pip install requests selenium webdriver-manager
```

### 步骤3: 准备ISO文件
下载KVM虚拟化系统ISO文件：
- **Ubuntu Server** (推荐): https://ubuntu.com/download/server
- **CentOS Stream**: https://www.centos.org/download/
- **Rocky Linux**: https://rockylinux.org/download/

### 步骤4: 自动安装
```batch
# 使用图形化界面
test\example_usage.bat

# 或直接命令行
python test\idrac_web_manager.py --host ************* --install-kvm "C:\Downloads\ubuntu-22.04-server-amd64.iso"
```

## 📖 详细使用说明

### 命令行参数
```batch
python test\idrac_web_manager.py [选项]

必需参数:
  --host IP地址          iDRAC的IP地址

可选参数:
  --username 用户名      登录用户名 (默认: root)
  --password 密码        登录密码 (默认: calvin)
  --install-kvm ISO路径  自动安装KVM系统
  --mount-iso ISO路径    仅挂载ISO文件
  --power 操作           电源控制 (on/off/reset)
  --open                 打开Web界面
  --scan 网络段          扫描iDRAC设备
```

### 使用示例

#### 1. 扫描网络中的iDRAC设备
```batch
python test\idrac_web_manager.py --scan 192.168.1
```

#### 2. 测试连接
```batch
python test\idrac_web_manager.py --host ************* --info
```

#### 3. 自动安装Ubuntu Server
```batch
python test\idrac_web_manager.py --host ************* --username root --password calvin --install-kvm "C:\ISOs\ubuntu-22.04-server-amd64.iso"
```

#### 4. 仅挂载ISO文件
```batch
python test\idrac_web_manager.py --host ************* --mount-iso "C:\ISOs\centos-stream-9.iso"
```

#### 5. 电源控制
```batch
# 重启服务器
python test\idrac_web_manager.py --host ************* --power reset

# 开机
python test\idrac_web_manager.py --host ************* --power on

# 关机
python test\idrac_web_manager.py --host ************* --power off
```

## 🔄 自动安装流程

当您运行 `--install-kvm` 命令时，工具会自动执行以下步骤：

1. **🔍 连接检查**: 验证iDRAC连接性
2. **🔐 自动登录**: 使用提供的凭据登录iDRAC Web界面
3. **💿 挂载ISO**: 将ISO文件挂载到虚拟CD/DVD驱动器
4. **⚙️ 设置启动**: 配置服务器从CD/DVD启动
5. **🔄 重启服务器**: 执行服务器重启
6. **🖥️ 打开控制台**: 自动打开虚拟控制台供您监控安装进度

## 🛠️ 故障排除

### Python相关问题
```batch
# 检查Python安装
python --version

# 检查pip
pip --version

# 重新安装包
pip install --upgrade requests selenium webdriver-manager
```

### 浏览器相关问题
```batch
# 检查Chrome
where chrome

# 检查Firefox  
where firefox

# 手动下载ChromeDriver
# 访问: https://chromedriver.chromium.org/
```

### 网络连接问题
```batch
# 测试iDRAC连接
ping *************

# 测试HTTPS端口
telnet ************* 443
```

### iDRAC访问问题
- 确认iDRAC IP地址正确
- 检查用户名和密码
- 确认iDRAC Web界面可以通过浏览器正常访问
- 检查防火墙设置

## 🔐 安全注意事项

1. **默认密码**: Dell iDRAC默认用户名/密码通常是 `root/calvin`，请及时修改
2. **网络安全**: 确保iDRAC网络访问受到适当限制
3. **SSL证书**: 工具会自动忽略SSL证书错误，生产环境请使用有效证书
4. **凭据保护**: 避免在脚本中硬编码密码

## 📞 技术支持

### 常见问题
1. **Q**: 为什么浏览器没有自动打开？
   **A**: 检查是否安装了Chrome/Firefox，或手动指定浏览器路径

2. **Q**: ISO挂载失败怎么办？
   **A**: 确认ISO文件路径正确，文件没有被占用

3. **Q**: 虚拟控制台无法访问？
   **A**: 检查iDRAC许可证是否支持虚拟控制台功能

### 获取帮助
- 查看详细日志输出
- 检查iDRAC事件日志
- 确认服务器硬件状态

## 🎉 成功案例

使用此工具，您可以：
- 在几分钟内完成远程服务器的KVM虚拟化系统安装
- 批量部署多台服务器
- 实现无人值守的自动化安装
- 通过Windows工作站管理远程Linux服务器

这个工具特别适合：
- 数据中心管理员
- 系统集成商
- DevOps工程师
- 虚拟化环境管理员
