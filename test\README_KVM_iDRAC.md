# KVM虚拟化环境 + iDRAC管理工具

这个工具集提供了完整的KVM虚拟化环境自动安装和iDRAC远程管理功能。

## 🚀 快速开始

### 1. 安装KVM虚拟化环境

```bash
# 在WSL或Linux系统中运行
./test/kvm_auto_installer.sh
```

这个脚本会自动：
- 检测操作系统类型（Ubuntu/Debian 或 CentOS/RHEL）
- 验证CPU虚拟化支持
- 安装KVM、QEMU、libvirt等组件
- 配置用户权限和网络桥接
- 创建管理工具脚本

### 2. 验证安装

```bash
# 运行增强的验证脚本（包含KVM检查）
./test/wsl_validation_test.sh
```

### 3. 使用iDRAC管理工具

```bash
# 基本用法
python3 test/idrac_web_manager.py --host *************

# 使用自定义凭据
python3 test/idrac_web_manager.py --host ************* --username admin --password mypassword

# 直接打开Web界面
python3 test/idrac_web_manager.py --host ************* --open

# 扫描网络中的iDRAC设备
python3 test/idrac_web_manager.py --scan 192.168.1
```

## 📋 功能特性

### KVM虚拟化环境
- ✅ 自动检测并安装适合的KVM组件
- ✅ 配置libvirt服务和网络桥接
- ✅ 用户权限自动配置
- ✅ 图形化管理界面（virt-manager）
- ✅ 命令行管理工具

### iDRAC远程管理
- ✅ 自动打开Web管理界面
- ✅ 网络扫描发现iDRAC设备
- ✅ 虚拟控制台访问
- ✅ 桌面快捷方式创建
- ✅ 连接性检查

### 浏览器集成
- ✅ 支持多种浏览器（Firefox、Chrome、Edge等）
- ✅ 自动处理SSL证书问题
- ✅ Web界面自动化操作

## 🛠️ 安装后的管理工具

安装完成后，会在用户主目录创建以下管理脚本：

### ~/kvm_manager.sh
```bash
# 查看所有虚拟机
~/kvm_manager.sh list

# 查看KVM服务状态
~/kvm_manager.sh status

# 查看虚拟网络
~/kvm_manager.sh networks

# 启动图形管理界面
~/kvm_manager.sh gui
```

### ~/idrac_connect.sh
```bash
# 连接到iDRAC
~/idrac_connect.sh *************

# 使用自定义凭据
~/idrac_connect.sh ************* admin mypassword
```

## 🔧 系统要求

### 硬件要求
- CPU支持硬件虚拟化（Intel VT-x 或 AMD-V）
- 至少4GB RAM（推荐8GB+）
- 足够的磁盘空间（推荐50GB+）

### 软件要求
- Ubuntu 18.04+ / Debian 10+ / CentOS 7+ / RHEL 7+
- Python 3.6+
- sudo权限

### 网络要求
- 能够访问iDRAC设备的网络连接
- 互联网连接（用于下载软件包）

## 📖 使用场景

### 1. 开发测试环境
```bash
# 安装KVM环境
./test/kvm_auto_installer.sh

# 创建测试虚拟机
virt-install --name test-vm --ram 2048 --disk size=20 --cdrom /path/to/iso
```

### 2. 服务器管理
```bash
# 扫描网络中的服务器
python3 test/idrac_web_manager.py --scan 192.168.1

# 连接到特定服务器
python3 test/idrac_web_manager.py --host ************* --open
```

### 3. 远程安装操作系统
1. 通过iDRAC连接到服务器
2. 挂载ISO镜像到虚拟媒体
3. 通过虚拟控制台进行安装

## 🔍 故障排除

### KVM安装问题
```bash
# 检查CPU虚拟化支持
grep -E '(vmx|svm)' /proc/cpuinfo

# 检查KVM模块
lsmod | grep kvm

# 验证libvirt服务
systemctl status libvirtd
```

### iDRAC连接问题
```bash
# 测试网络连接
ping *************

# 检查端口开放
nmap -p 443 *************

# 验证SSL证书
openssl s_client -connect *************:443
```

### 权限问题
```bash
# 检查用户组
groups

# 重新添加用户到组
sudo usermod -aG libvirt,kvm $(whoami)

# 注销并重新登录
```

## 🔐 安全注意事项

1. **默认密码**: iDRAC默认用户名/密码通常是 root/calvin，请及时修改
2. **网络安全**: 确保iDRAC网络访问受到适当限制
3. **SSL证书**: 生产环境中应使用有效的SSL证书
4. **用户权限**: 仅给予必要的KVM管理权限

## 📞 支持

如果遇到问题，请：
1. 运行验证脚本检查环境
2. 查看日志文件
3. 检查系统要求是否满足

## 🔄 更新日志

- v1.0: 初始版本，支持KVM自动安装和iDRAC管理
- 支持Ubuntu/Debian和CentOS/RHEL系统
- 集成浏览器自动化操作
- 提供完整的管理工具集
