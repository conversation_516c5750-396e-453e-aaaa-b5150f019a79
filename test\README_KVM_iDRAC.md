# KVM虚拟化环境 + iDRAC管理工具

这个工具集提供了完整的KVM虚拟化环境自动安装和iDRAC远程管理功能。

## 🚀 快速开始

### Windows用户 - 通过iDRAC自动安装KVM虚拟化系统

#### 1. 安装依赖
```batch
# 双击运行或在命令提示符中执行
test\setup_dependencies.bat
```

#### 2. 自动安装KVM虚拟化系统
```batch
# 使用图形化界面
test\install_kvm_via_idrac.bat

# 或直接命令行
python test\idrac_web_manager.py --host ************* --install-kvm "C:\path\to\ubuntu-server.iso"
```

#### 3. 其他操作
```batch
# 仅挂载ISO文件
python test\idrac_web_manager.py --host ************* --mount-iso "C:\path\to\file.iso"

# 电源控制
python test\idrac_web_manager.py --host ************* --power reset

# 扫描网络中的iDRAC设备
python test\idrac_web_manager.py --scan 192.168.1
```

### Linux/WSL用户 - 本地安装KVM环境

#### 1. 安装KVM虚拟化环境
```bash
# 在WSL或Linux系统中运行
./test/kvm_auto_installer.sh
```

#### 2. 验证安装
```bash
# 运行增强的验证脚本（包含KVM检查）
./test/wsl_validation_test.sh
```

#### 3. 使用iDRAC管理工具
```bash
# 基本用法
python3 test/idrac_web_manager.py --host *************

# 自动安装KVM系统到远程服务器
python3 test/idrac_web_manager.py --host ************* --install-kvm "/path/to/kvm.iso"

# 扫描网络中的iDRAC设备
python3 test/idrac_web_manager.py --scan 192.168.1
```

## 📋 功能特性

### Windows自动化安装 🆕
- ✅ **一键自动安装**: 通过iDRAC自动安装KVM虚拟化系统到远程服务器
- ✅ **浏览器自动化**: 使用Selenium自动操作iDRAC Web界面
- ✅ **ISO自动挂载**: 自动挂载ISO文件到虚拟媒体
- ✅ **电源控制**: 自动重启服务器并从CD启动
- ✅ **虚拟控制台**: 自动打开控制台监控安装进度
- ✅ **图形化界面**: Windows批处理脚本提供友好的用户界面

### KVM虚拟化环境
- ✅ 自动检测并安装适合的KVM组件
- ✅ 配置libvirt服务和网络桥接
- ✅ 用户权限自动配置
- ✅ 图形化管理界面（virt-manager）
- ✅ 命令行管理工具

### iDRAC远程管理
- ✅ 自动打开Web管理界面
- ✅ 网络扫描发现iDRAC设备
- ✅ 虚拟控制台访问
- ✅ 桌面快捷方式创建
- ✅ 连接性检查
- ✅ 自动登录和操作

### 浏览器集成
- ✅ 支持多种浏览器（Firefox、Chrome、Edge等）
- ✅ 自动处理SSL证书问题
- ✅ Web界面自动化操作
- ✅ 无头模式支持（后台运行）

## 🛠️ 安装后的管理工具

安装完成后，会在用户主目录创建以下管理脚本：

### ~/kvm_manager.sh
```bash
# 查看所有虚拟机
~/kvm_manager.sh list

# 查看KVM服务状态
~/kvm_manager.sh status

# 查看虚拟网络
~/kvm_manager.sh networks

# 启动图形管理界面
~/kvm_manager.sh gui
```

### ~/idrac_connect.sh
```bash
# 连接到iDRAC
~/idrac_connect.sh *************

# 使用自定义凭据
~/idrac_connect.sh ************* admin mypassword
```

## 🔧 系统要求

### 硬件要求
- CPU支持硬件虚拟化（Intel VT-x 或 AMD-V）
- 至少4GB RAM（推荐8GB+）
- 足够的磁盘空间（推荐50GB+）

### 软件要求
- Ubuntu 18.04+ / Debian 10+ / CentOS 7+ / RHEL 7+
- Python 3.6+
- sudo权限

### 网络要求
- 能够访问iDRAC设备的网络连接
- 互联网连接（用于下载软件包）

## 📖 使用场景

### 1. Windows环境下的远程服务器自动化部署 🆕
```batch
# 自动安装Ubuntu Server到Dell服务器
test\install_kvm_via_idrac.bat

# 选择操作 -> 1 (自动安装KVM虚拟化系统)
# 选择ISO -> 1 (Ubuntu Server)
# 输入ISO路径 -> C:\Downloads\ubuntu-22.04-server-amd64.iso
# 确认安装 -> y
```

**完整自动化流程**:
1. 🔍 自动检测iDRAC连接性
2. 🔐 自动登录iDRAC Web界面
3. 💿 自动挂载KVM ISO到虚拟媒体
4. ⚡ 自动设置从CD启动
5. 🔄 自动重启服务器
6. 🖥️ 自动打开虚拟控制台监控安装

### 2. 批量服务器部署
```batch
# 扫描网络中的所有iDRAC设备
python test\idrac_web_manager.py --scan 192.168.1

# 批量安装（可以编写脚本循环处理）
for /f %%i in (server_list.txt) do (
    python test\idrac_web_manager.py --host %%i --install-kvm "C:\ISOs\ubuntu-server.iso"
)
```

### 3. 开发测试环境
```bash
# Linux/WSL环境 - 本地KVM安装
./test/kvm_auto_installer.sh

# 创建测试虚拟机
virt-install --name test-vm --ram 2048 --disk size=20 --cdrom /path/to/iso
```

### 4. 混合环境管理
- **Windows工作站**: 使用iDRAC工具管理远程物理服务器
- **Linux服务器**: 运行KVM虚拟化环境
- **统一管理**: 通过同一套工具管理本地和远程环境

## 🔍 故障排除

### KVM安装问题
```bash
# 检查CPU虚拟化支持
grep -E '(vmx|svm)' /proc/cpuinfo

# 检查KVM模块
lsmod | grep kvm

# 验证libvirt服务
systemctl status libvirtd
```

### iDRAC连接问题
```bash
# 测试网络连接
ping *************

# 检查端口开放
nmap -p 443 *************

# 验证SSL证书
openssl s_client -connect *************:443
```

### 权限问题
```bash
# 检查用户组
groups

# 重新添加用户到组
sudo usermod -aG libvirt,kvm $(whoami)

# 注销并重新登录
```

## 🔐 安全注意事项

1. **默认密码**: iDRAC默认用户名/密码通常是 root/calvin，请及时修改
2. **网络安全**: 确保iDRAC网络访问受到适当限制
3. **SSL证书**: 生产环境中应使用有效的SSL证书
4. **用户权限**: 仅给予必要的KVM管理权限

## 📞 支持

如果遇到问题，请：
1. 运行验证脚本检查环境
2. 查看日志文件
3. 检查系统要求是否满足

## 🔄 更新日志

- v1.0: 初始版本，支持KVM自动安装和iDRAC管理
- 支持Ubuntu/Debian和CentOS/RHEL系统
- 集成浏览器自动化操作
- 提供完整的管理工具集
